import { NextConfig } from "next";
import { Configuration } from "webpack";

const nextConfig: NextConfig = {
  webpack: (
    config: Configuration,
    options: { isServer: boolean; dev: boolean }
  ) => {
    const { isServer } = options;
    config.plugins?.push(
      new (require("@module-federation/nextjs-mf").NextFederationPlugin)({
        name: "authApp",
        filename: "static/chunks/remoteEntry.js",
        exposes: {
          "./Login": "./pages/login.tsx",
        },
        shared: {
          react: { singleton: true },
          "react-dom": { singleton: true },
        },
      })
    );
    return config;
  },
};

export default nextConfig;
