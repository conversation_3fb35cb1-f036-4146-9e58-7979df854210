hoistPattern:
  - '*'
hoistedDependencies:
  '@esbuild/win32-x64@0.17.19':
    '@esbuild/win32-x64': private
  '@img/sharp-win32-x64@0.34.2':
    '@img/sharp-win32-x64': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@modern-js/node-bundle-require@2.67.6':
    '@modern-js/node-bundle-require': private
  '@modern-js/utils@2.67.6':
    '@modern-js/utils': private
  '@module-federation/bridge-react-webpack-plugin@0.16.0':
    '@module-federation/bridge-react-webpack-plugin': private
  '@module-federation/cli@0.16.0(typescript@5.8.3)':
    '@module-federation/cli': private
  '@module-federation/data-prefetch@0.16.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@module-federation/data-prefetch': private
  '@module-federation/dts-plugin@0.16.0(typescript@5.8.3)':
    '@module-federation/dts-plugin': private
  '@module-federation/enhanced@0.16.0(@rspack/core@1.4.4(@swc/helpers@0.5.17))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(typescript@5.8.3)(webpack@5.99.9)':
    '@module-federation/enhanced': private
  '@module-federation/error-codes@0.16.0':
    '@module-federation/error-codes': private
  '@module-federation/inject-external-runtime-core-plugin@0.16.0(@module-federation/runtime-tools@0.16.0)':
    '@module-federation/inject-external-runtime-core-plugin': private
  '@module-federation/managers@0.16.0':
    '@module-federation/managers': private
  '@module-federation/manifest@0.16.0(typescript@5.8.3)':
    '@module-federation/manifest': private
  '@module-federation/node@2.7.8(@rspack/core@1.4.4(@swc/helpers@0.5.17))(next@15.3.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(typescript@5.8.3)(webpack@5.99.9)':
    '@module-federation/node': private
  '@module-federation/rspack@0.16.0(@rspack/core@1.4.4(@swc/helpers@0.5.17))(typescript@5.8.3)':
    '@module-federation/rspack': private
  '@module-federation/runtime-core@0.16.0':
    '@module-federation/runtime-core': private
  '@module-federation/runtime-tools@0.16.0':
    '@module-federation/runtime-tools': private
  '@module-federation/runtime@0.16.0':
    '@module-federation/runtime': private
  '@module-federation/sdk@0.16.0':
    '@module-federation/sdk': private
  '@module-federation/third-party-dts-extractor@0.16.0':
    '@module-federation/third-party-dts-extractor': private
  '@module-federation/webpack-bundler-runtime@0.16.0':
    '@module-federation/webpack-bundler-runtime': private
  '@next/env@15.3.5':
    '@next/env': private
  '@next/swc-win32-x64-msvc@15.3.5':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@rspack/binding-win32-x64-msvc@1.4.4':
    '@rspack/binding-win32-x64-msvc': private
  '@rspack/binding@1.4.4':
    '@rspack/binding': private
  '@rspack/core@1.4.4(@swc/helpers@0.5.17)':
    '@rspack/core': private
  '@rspack/lite-tapable@1.0.1':
    '@rspack/lite-tapable': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/semver@7.5.8':
    '@types/semver': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  accepts@1.3.8:
    accepts: private
  acorn@8.15.0:
    acorn: private
  adm-zip@0.5.16:
    adm-zip: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@8.17.1:
    ajv: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-styles@4.3.0:
    ansi-styles: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  axios@1.10.0:
    axios: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  btoa@1.2.1:
    btoa: private
  buffer-from@1.1.2:
    buffer-from: private
  busboy@1.6.0:
    busboy: private
  cache-content-type@1.0.1:
    cache-content-type: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chalk@3.0.0:
    chalk: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  client-only@0.0.1:
    client-only: private
  co@4.6.0:
    co: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@11.1.0:
    commander: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookies@0.9.1:
    cookies: private
  cron-parser@4.9.0:
    cron-parser: private
  csstype@3.1.3:
    csstype: private
  date-format@4.0.14:
    date-format: private
  debug@4.4.1:
    debug: private
  deep-equal@1.0.1:
    deep-equal: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-libc@2.0.4:
    detect-libc: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.180:
    electron-to-chromium: private
  encodeurl@1.0.2:
    encodeurl: private
  encoding@0.1.13:
    encoding: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.17.19:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  eslint-scope@5.1.1:
    eslint-scope: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  events@3.3.0:
    events: private
  expand-tilde@2.0.2:
    expand-tilde: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fill-range@7.1.1:
    fill-range: private
  find-file-up@2.0.1:
    find-file-up: private
  find-pkg@2.0.0:
    find-pkg: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  form-data@4.0.3:
    form-data: private
  fresh@0.5.2:
    fresh: private
  fs-extra@9.1.0:
    fs-extra: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@5.1.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  global-modules@1.0.0:
    global-modules: private
  global-prefix@1.0.2:
    global-prefix: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  homedir-polyfill@1.0.3:
    homedir-polyfill: private
  http-assert@1.5.0:
    http-assert: private
  http-errors@1.8.1:
    http-errors: private
  iconv-lite@0.6.3:
    iconv-lite: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-regex@1.2.1:
    is-regex: private
  is-windows@1.0.2:
    is-windows: private
  isexe@2.0.0:
    isexe: private
  isomorphic-ws@5.0.0(ws@8.18.0):
    isomorphic-ws: private
  jest-worker@27.5.1:
    jest-worker: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  jsonfile@6.1.0:
    jsonfile: private
  keygrip@1.1.0:
    keygrip: private
  koa-compose@4.1.0:
    koa-compose: private
  koa-convert@2.0.0:
    koa-convert: private
  koa@2.16.1:
    koa: private
  loader-runner@4.3.0:
    loader-runner: private
  lodash.clonedeepwith@4.5.0:
    lodash.clonedeepwith: private
  lodash@4.17.21:
    lodash: private
  log4js@6.9.1:
    log4js: private
  long-timeout@0.1.1:
    long-timeout: private
  luxon@3.6.1:
    luxon: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  negotiator@0.6.3:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  node-fetch@2.7.0(encoding@0.1.13):
    node-fetch: private
  node-releases@2.0.19:
    node-releases: private
  node-schedule@2.1.1:
    node-schedule: private
  on-finished@2.4.1:
    on-finished: private
  only@0.0.2:
    only: private
  parse-passwd@1.0.0:
    parse-passwd: private
  parseurl@1.3.3:
    parseurl: private
  path-parse@1.0.7:
    path-parse: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  postcss@8.4.31:
    postcss: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  queue-microtask@1.2.3:
    queue-microtask: private
  rambda@9.4.2:
    rambda: private
  randombytes@2.1.0:
    randombytes: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-dir@1.0.1:
    resolve-dir: private
  resolve@1.22.8:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rslog@1.2.9:
    rslog: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  semver@7.7.2:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sharp@0.34.2:
    sharp: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sorted-array-functions@1.3.0:
    sorted-array-functions: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  statuses@1.5.0:
    statuses: private
  streamroller@3.1.5:
    streamroller: private
  streamsearch@1.1.0:
    streamsearch: private
  styled-jsx@5.1.6(react@19.1.0):
    styled-jsx: private
  supports-color@7.2.0:
    supports-color: private
  supports-color@8.1.1:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tapable@2.2.2:
    tapable: private
  terser-webpack-plugin@5.3.14(webpack@5.99.9):
    terser-webpack-plugin: private
  terser@5.43.1:
    terser: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  tr46@0.0.3:
    tr46: private
  tslib@2.8.1:
    tslib: private
  tsscmp@1.0.6:
    tsscmp: private
  type-is@1.6.18:
    type-is: private
  typescript@5.8.3:
    typescript: private
  undici-types@6.21.0:
    undici-types: private
  universalify@2.0.1:
    universalify: private
  upath@2.0.1:
    upath: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  vary@1.1.2:
    vary: private
  watchpack@2.4.4:
    watchpack: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack@5.99.9:
    webpack: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@1.3.1:
    which: private
  ws@8.18.0:
    ws: private
  ylru@1.4.0:
    ylru: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Tue, 08 Jul 2025 00:28:28 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.4'
  - '@emnapi/runtime@1.4.4'
  - '@emnapi/wasi-threads@1.0.3'
  - '@esbuild/android-arm64@0.17.19'
  - '@esbuild/android-arm@0.17.19'
  - '@esbuild/android-x64@0.17.19'
  - '@esbuild/darwin-arm64@0.17.19'
  - '@esbuild/darwin-x64@0.17.19'
  - '@esbuild/freebsd-arm64@0.17.19'
  - '@esbuild/freebsd-x64@0.17.19'
  - '@esbuild/linux-arm64@0.17.19'
  - '@esbuild/linux-arm@0.17.19'
  - '@esbuild/linux-ia32@0.17.19'
  - '@esbuild/linux-loong64@0.17.19'
  - '@esbuild/linux-mips64el@0.17.19'
  - '@esbuild/linux-ppc64@0.17.19'
  - '@esbuild/linux-riscv64@0.17.19'
  - '@esbuild/linux-s390x@0.17.19'
  - '@esbuild/linux-x64@0.17.19'
  - '@esbuild/netbsd-x64@0.17.19'
  - '@esbuild/openbsd-x64@0.17.19'
  - '@esbuild/sunos-x64@0.17.19'
  - '@esbuild/win32-arm64@0.17.19'
  - '@esbuild/win32-ia32@0.17.19'
  - '@img/sharp-darwin-arm64@0.34.2'
  - '@img/sharp-darwin-x64@0.34.2'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.34.2'
  - '@img/sharp-linux-arm@0.34.2'
  - '@img/sharp-linux-s390x@0.34.2'
  - '@img/sharp-linux-x64@0.34.2'
  - '@img/sharp-linuxmusl-arm64@0.34.2'
  - '@img/sharp-linuxmusl-x64@0.34.2'
  - '@img/sharp-wasm32@0.34.2'
  - '@img/sharp-win32-arm64@0.34.2'
  - '@img/sharp-win32-ia32@0.34.2'
  - '@napi-rs/wasm-runtime@0.2.11'
  - '@next/swc-darwin-arm64@15.3.5'
  - '@next/swc-darwin-x64@15.3.5'
  - '@next/swc-linux-arm64-gnu@15.3.5'
  - '@next/swc-linux-arm64-musl@15.3.5'
  - '@next/swc-linux-x64-gnu@15.3.5'
  - '@next/swc-linux-x64-musl@15.3.5'
  - '@next/swc-win32-arm64-msvc@15.3.5'
  - '@rspack/binding-darwin-arm64@1.4.4'
  - '@rspack/binding-darwin-x64@1.4.4'
  - '@rspack/binding-linux-arm64-gnu@1.4.4'
  - '@rspack/binding-linux-arm64-musl@1.4.4'
  - '@rspack/binding-linux-x64-gnu@1.4.4'
  - '@rspack/binding-linux-x64-musl@1.4.4'
  - '@rspack/binding-wasm32-wasi@1.4.4'
  - '@rspack/binding-win32-arm64-msvc@1.4.4'
  - '@rspack/binding-win32-ia32-msvc@1.4.4'
  - '@tybys/wasm-util@0.9.0'
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Full Stack Dev Projects\Posts Project\MFKB Task Board\Frontend Knowledge Base App\auth-app\node_modules\.pnpm
virtualStoreDirMaxLength: 60
