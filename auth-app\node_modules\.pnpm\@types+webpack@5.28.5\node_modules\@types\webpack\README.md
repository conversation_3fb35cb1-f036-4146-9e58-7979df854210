# Installation
> `npm install --save @types/webpack`

# Summary
This package contains type definitions for webpack (https://github.com/webpack/webpack).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack/index.d.ts)
````ts
/// <reference types="node" />
/* eslint-disable-next-line @definitelytyped/no-self-import */
import webpack = require("webpack");
export = webpack;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 20:08:00 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node), [tapable](https://npmjs.com/package/tapable), [webpack](https://npmjs.com/package/webpack)

# Credits
These definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/tkqubo), [<PERSON>](https://github.com/bumbleblym), [<PERSON>](https://github.com/bcherny), [<PERSON>](https://github.com/tommytroylin), [Mohsen Azimi](https://github.com/mohsen1), [Jonathan Creamer](https://github.com/jcreamer898), [Alan Agius](https://github.com/alan-agius4), [Dennis George](https://github.com/dennispg), [Christophe Hurpeau](https://github.com/christophehurpeau), [ZSkycat](https://github.com/ZSkycat), [John Reilly](https://github.com/johnnyreilly), [Ryan Waskiewicz](https://github.com/rwaskiewicz), [Kyle Uehlein](https://github.com/kuehlein), [Grgur Grisogono](https://github.com/grgur), [Rubens Pinheiro Gonçalves Cavalcante](https://github.com/rubenspgcavalcante), [Anders Kaseorg](https://github.com/andersk), [Felix Haus](https://github.com/ofhouse), [Daniel Chin](https://github.com/danielthank), [Daiki Ihara](https://github.com/sasurau4), [Dion Shi](https://github.com/dionshihk), [Piotr Błażejewicz](https://github.com/peterblazejewicz), and [Michał Grzegorzewski](https://github.com/spamshaker).
