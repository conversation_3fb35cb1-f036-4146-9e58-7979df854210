import { NextConfig } from "next";
import { Configuration } from "webpack";

const nextConfig: NextConfig = {
  webpack: (
    config: Configuration,
    options: { isServer: boolean; dev: boolean }
  ) => {
    const { isServer } = options;
    // TODO: Update with noteApp federation config when ready
    // Example federation config for notesApp:
    // config.plugins?.push(
    //   new (require('@module-federation/nextjs-mf').NextFederationPlugin)({
    //     name: 'notesApp',
    //     filename: 'static/chunks/remoteEntry.js',
    //     exposes: {
    //       './NotesEditor': './pages/notes.tsx',
    //     },
    //     shared: {
    //       react: { singleton: true },
    //       'react-dom': { singleton: true },
    //     },
    //   })
    // );
    return config;
  },
};

export default nextConfig;
