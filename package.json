{"name": "frontend-knowledge-base-app", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.4", "devDependencies": {"@eslint/eslintrc": "^3", "@types/webpack": "^5.28.5", "autoprefixer": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.0.0", "tailwindcss": "^3.0.0", "typescript": "^5.0.0", "webpack": "^5.99.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19"}, "dependencies": {"@module-federation/nextjs-mf": "^8.8.32", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0"}}