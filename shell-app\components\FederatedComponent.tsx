import dynamic from 'next/dynamic';
import { Suspense } from 'react';

const FederatedComponent = ({ scope, module, fallback = null }: { scope: string; module: string; fallback?: React.ReactNode }) => {
  const Component = dynamic(
    // Dynamically load the remote module using scope and module props
    () =>
      (window as any)[scope].get(module).then((factory: any) => {
        const Module = factory();
        return Module;
      }),
    { loading: () => fallback }
  );
  return (
    <Suspense fallback={fallback}>
      <Component />
    </Suspense>
  );
};

export default FederatedComponent;
