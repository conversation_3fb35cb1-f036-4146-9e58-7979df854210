import { NextConfig } from "next";
import { Configuration } from "webpack";
//import { NextFederationPlugin } from "@module-federation/nextjs-mf";



const nextConfig: NextConfig = {
  webpack: (
    config: Configuration,
    options: { isServer: boolean; dev: boolean }
  ) => {
    const { isServer } = options;
    const federationConfig = {
      name: "shellApp",
      _extraOptions: {
        automaticAsyncBoundary: true, // Recommended
      },
      remotes: {
        authApp: `authApp@http://localhost:3001/_next/static/${
          isServer ? "ssr" : "chunks"
        }/remoteEntry.js`,
        notesApp: `notesApp@http://localhost:3002/_next/static/${
          isServer ? "ssr" : "chunks"
        }/remoteEntry.js`,
      },
      shared: {
        react: { singleton: true },
        "react-dom": { singleton: true },
      },
    };
    config.plugins?.push(
      new (require("@module-federation/nextjs-mf").NextFederationPlugin)({
        ...federationConfig,
        filename: "static/chunks/remoteEntry.js",
      })
    );
    return config;
  },
};

export default nextConfig;
